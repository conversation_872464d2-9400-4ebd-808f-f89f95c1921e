import os
import pandas as pd
from sqlalchemy import create_engine
import pyodbc

def belirli_excel_listesini_accesse_birlestir(klasor_yolu, dosya_listesi, access_db_adi='birlesik_veri.accdb'):
    """
    Belirli bir listedeki Excel dosyalarını birleştirir ve Access veritabanına kaydeder.

    Parametreler:
    - klasor_yolu: Excel dosyalarının bulunduğu klasör yolu
    - dosya_listesi: Birleştirilecek dosya adlarının listesi (örn: ['5416.xlsx', '5417.xlsx'])
    - access_db_adi: Oluşturulacak Access veritabanı adı
    """

    # Access veritabanı yolunu oluştur
    access_yolu = os.path.join(klasor_yolu, access_db_adi)

    # Eğer Access dosyası yoksa oluştur
    if not os.path.exists(access_yolu):
        try:
            # win32com kullanarak Access veritabanı oluştur
            import win32com.client
            access_app = win32com.client.Dispatch("Access.Application")
            access_app.NewCurrentDatabase(access_yolu)
            access_app.CloseCurrentDatabase()
            access_app.Quit()
            print(f"Access veritabanı oluşturuldu: {access_yolu}")
        except ImportError:
            print("win32com modülü bulunamadı. pywin32 yüklemeyi deneyin: pip install pywin32")
            return
        except Exception as e:
            print(f"Access veritabanı oluşturulurken hata: {str(e)}")
            # Alternatif olarak boş bir .accdb dosyası oluşturmayı dene
            try:
                # Basit bir şekilde boş dosya oluştur ve sonra bağlantıyı dene
                with open(access_yolu, 'w') as f:
                    pass
                os.remove(access_yolu)  # Boş dosyayı sil
                print("Access uygulaması bulunamadı. Lütfen manuel olarak boş bir .accdb dosyası oluşturun.")
                return
            except:
                return

    # Access veritabanı bağlantısı oluştur
    conn_str = f"DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={access_yolu};"
    engine = create_engine(f"access+pyodbc:///?odbc_connect={conn_str}")

    # Tüm dosyaları birleştir ve Access'e yaz
    for dosya_adi in dosya_listesi:
        dosya_yolu = os.path.join(klasor_yolu, dosya_adi)
        
        try:
            # Excel'i oku
            veri = pd.read_excel(dosya_yolu)
            
            # Tablo adı olarak dosya adını temizle (örn: 5416.xlsx -> tablo_5416)
            tablo_adi = f"tablo_{os.path.splitext(dosya_adi)[0]}"
            
            # Access'e yaz
            veri.to_sql(tablo_adi, engine, if_exists='replace', index=False)
            print(f"{dosya_adi} başarıyla {tablo_adi} tablosuna aktarıldı")
            
        except Exception as e:
            print(f"{dosya_adi} işlenirken hata oluştu: {str(e)}")
    
    print(f"\nTüm dosyalar {access_yolu} Access veritabanına kaydedildi")
    print(f"Toplam {len(dosya_listesi)} dosya işlendi")

# Örnek Kullanım:
if __name__ == "__main__":
    # Klasör yolunu ve dosya listesini belirtin
    dosya_listesi = [
        '5416.xlsx',
        '5417.xlsx',
        '5418.xlsx',
        '5419.xlsx',
        '5420.xlsx',
        '5422.xlsx'
    ]
    
    belirli_excel_listesini_accesse_birlestir(
        klasor_yolu=r"C:\\Users\\<USER>\\Desktop\\Hakkediş Desi detay\\Data",  # Excel dosyalarının bulunduğu klasör
        dosya_listesi=dosya_listesi,
        access_db_adi='birlesik_veriler.accdb'  # Oluşturulacak Access dosya adı
    )